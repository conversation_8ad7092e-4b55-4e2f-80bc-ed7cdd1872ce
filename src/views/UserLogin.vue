<template>
  <div class="user-login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button 
          type="info" 
          :icon="ArrowLeft" 
          circle 
          @click="goBack"
          class="back-btn"
        />
      </div>
      
      <!-- 登录卡片 -->
      <el-card class="login-card" shadow="always">
        <div class="login-header">
          <div class="icon-wrapper">
            <el-icon class="login-icon">
              <User />
            </el-icon>
          </div>
          <h2 class="login-title">用户登录</h2>
          <p class="login-subtitle">欢迎回来，请登录您的账户</p>
        </div>
        
        <!-- 登录表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名/邮箱"
              :prefix-icon="User"
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>
          
          <el-form-item>
            <div class="form-options">
              <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
              <el-link type="primary" :underline="false" @click="goToForgotPassword">忘记密码？</el-link>
            </div>
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              <el-icon v-if="!loading"><UserFilled /></el-icon>
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>
        </el-form>
        
        <!-- 注册链接 -->
        <div class="register-link">
          <span>还没有账户？</span>
          <el-link 
            type="success" 
            :underline="false" 
            @click="goToRegister"
            class="register-btn"
          >
            立即注册
          </el-link>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, User, Lock, UserFilled } from '@element-plus/icons-vue'
import { userAPI } from '@/api/user'

export default {
  name: 'UserLogin',
  components: {
    UserFilled
  },
  setup() {
    const router = useRouter()
    const loginFormRef = ref()
    const loading = ref(false)
    
    // 登录表单数据
    const loginForm = reactive({
      username: '',
      password: '',
      remember: false
    })
    
    // 表单验证规则
    const loginRules = {
      username: [
        { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
        { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
      ]
    }
    
    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }
    
    // 跳转到注册页面
    const goToRegister = () => {
      router.push('/user/register')
    }
    
    // 处理登录
    const handleLogin = async () => {
      if (!loginFormRef.value) return

      try {
        const valid = await loginFormRef.value.validate()
        if (valid) {
          loading.value = true

          try {
            // 调用登录API
            const response = await userAPI.login({
              username: loginForm.username,
              password: loginForm.password
            })

            if (response.success) {
              ElMessage.success(response.message || '登录成功！')

              // 保存用户信息到localStorage
              localStorage.setItem('userInfo', JSON.stringify(response.data))
              localStorage.setItem('isLoggedIn', 'true')

              // 登录成功后跳转到商城主页
              router.push('/mall')

              console.log('登录成功，用户信息:', response.data)
            } else {
              ElMessage.error(response.message || '登录失败')
            }
          } catch (error) {
            console.error('登录请求失败:', error)
            ElMessage.error(error.message || '登录失败，请检查网络连接')
          } finally {
            loading.value = false
          }
        }
      } catch (error) {
        console.log('表单验证失败:', error)
      }
    }

    // 跳转到忘记密码页面
    const goToForgotPassword = () => {
      router.push('/forgot-password')
    }

    return {
      loginFormRef,
      loginForm,
      loginRules,
      loading,
      goBack,
      goToRegister,
      goToForgotPassword,
      handleLogin,
      ArrowLeft,
      User,
      Lock,
      UserFilled
    }
  }
}
</script>

<style scoped>
/* 主容器 */
.user-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 50%, #6b8e5a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 20px;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.3;
  }
}

/* 主要内容 */
.main-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 400px;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: -60px;
  left: 0;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateX(-5px);
}

/* 登录卡片 */
.login-card {
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-card :deep(.el-card__body) {
  padding: 40px;
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.icon-wrapper {
  margin-bottom: 20px;
}

.login-icon {
  font-size: 4rem;
  color: #4a7c59;
}

.login-title {
  font-size: 2rem;
  font-weight: 600;
  color: #2d5a27;
  margin-bottom: 10px;
}

.login-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

/* 登录表单 */
.login-form {
  margin-top: 30px;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.login-form :deep(.el-input__inner) {
  border-radius: 10px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
}

.login-form :deep(.el-input__inner:focus) {
  border-color: #4a7c59;
  box-shadow: 0 0 0 2px rgba(74, 124, 89, 0.2);
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 25px;
  background: linear-gradient(45deg, #4a7c59, #6b8e5a);
  border: none;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: linear-gradient(45deg, #3d6b4a, #4a7c59);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(74, 124, 89, 0.3);
}

/* 注册链接 */
.register-link {
  text-align: center;
  margin-top: 20px;
  color: #666;
}

.register-btn {
  margin-left: 8px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card :deep(.el-card__body) {
    padding: 30px 20px;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
  
  .back-button {
    top: -50px;
  }
}
</style>
