<template>
  <div class="orders-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <el-button 
          type="text" 
          @click="goBack" 
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>我的订单</h1>
      </div>
    </div>

    <!-- 订单统计 -->
    <div class="order-stats" v-if="statistics">
      <div class="stat-item">
        <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
        <div class="stat-label">全部订单</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ statistics.statusCounts?.pending || 0 }}</div>
        <div class="stat-label">待支付</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ statistics.statusCounts?.paid || 0 }}</div>
        <div class="stat-label">已支付</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ statistics.statusCounts?.shipped || 0 }}</div>
        <div class="stat-label">已发货</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ statistics.statusCounts?.completed || 0 }}</div>
        <div class="stat-label">已完成</div>
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="orders-list">
      <div v-if="loading" class="loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>

      <div v-else-if="orders.length === 0" class="empty-state">
        <el-icon><ShoppingCart /></el-icon>
        <p>暂无订单</p>
        <el-button type="primary" @click="goToMall">去购物</el-button>
      </div>

      <div v-else>
        <div 
          v-for="order in orders" 
          :key="order.id" 
          class="order-card"
        >
          <div class="order-header">
            <div class="order-info">
              <span class="order-no">订单号：{{ order.order_no }}</span>
              <span class="order-time">{{ formatTime(order.create_time) }}</span>
            </div>
            <div class="order-status">
              <el-tag :type="getStatusType(order.status)">
                {{ getStatusText(order.status) }}
              </el-tag>
            </div>
          </div>

          <div class="order-content">
            <div class="order-amount">
              <span class="amount-label">订单金额：</span>
              <span class="amount-value">¥{{ order.total_amount }}</span>
            </div>
            <div class="order-items">
              <span>共 {{ order.item_count }} 件商品</span>
            </div>
          </div>

          <div class="order-actions">
            <el-button 
              size="small" 
              @click="viewOrderDetail(order.id)"
            >
              查看详情
            </el-button>
            
            <el-button
              v-if="order.status === 0"
              type="primary"
              size="small"
              @click="payOrder(order)"
            >
              立即支付
            </el-button>

            <el-button
              v-if="order.status === 0"
              type="success"
              size="small"
              @click="simulatePaySuccess(order)"
            >
              模拟支付成功
            </el-button>

            <el-button
              v-if="order.status === 0"
              type="info"
              size="small"
              @click="queryPayStatus(order)"
            >
              查询支付状态
            </el-button>

            <el-button
              v-if="order.status === 0"
              type="warning"
              size="small"
              @click="forceConfirmPay(order)"
            >
              强制确认支付（测试）
            </el-button>
            
            <el-button 
              v-if="order.status === 0" 
              size="small"
              @click="cancelOrder(order.id)"
            >
              取消订单
            </el-button>
            
            <el-button 
              v-if="order.status === 2" 
              type="success" 
              size="small"
              @click="confirmOrder(order.id)"
            >
              确认收货
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ShoppingCart, Loading } from '@element-plus/icons-vue'
import ordersAPI from '@/api/orders'

export default {
  name: 'OrdersList',
  components: {
    ArrowLeft,
    ShoppingCart,
    Loading
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const orders = ref([])
    const statistics = ref(null)

    // 获取当前用户ID（从localStorage获取）
    const getCurrentUserId = () => {
      try {
        const userInfo = localStorage.getItem('userInfo')
        if (userInfo) {
          const userData = JSON.parse(userInfo)
          return userData.id
        }
        return null
      } catch (error) {
        console.error('获取用户ID失败:', error)
        return null
      }
    }

    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }

    // 去商城
    const goToMall = () => {
      router.push('/mall')
    }

    // 加载订单列表
    const loadOrders = async () => {
      loading.value = true
      try {
        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }
        
        const response = await ordersAPI.getUserOrders(userId)
        if (response.success) {
          orders.value = response.data
        } else {
          ElMessage.error(response.message || '加载订单失败')
        }
      } catch (error) {
        console.error('加载订单失败:', error)
        ElMessage.error('加载订单失败')
      } finally {
        loading.value = false
      }
    }

    // 加载订单统计
    const loadStatistics = async () => {
      try {
        const userId = getCurrentUserId()

        if (!userId) {
          return // 如果用户未登录，直接返回，不显示错误（因为会在loadOrders中处理）
        }
        
        const response = await ordersAPI.getOrderStatistics(userId)
        if (response.success) {
          statistics.value = response.data
        }
      } catch (error) {
        console.error('加载订单统计失败:', error)
      }
    }

    // 查看订单详情
    const viewOrderDetail = (orderId) => {
      router.push(`/order-detail/${orderId}`)
    }

    // 支付订单
    const payOrder = async (order) => {
      const payUrl = `http://localhost:8082/api/alipay/pay?subject=${encodeURIComponent('订单支付')}&traceNo=${order.order_no}&totalAmount=${order.total_amount}`

      try {
        ElMessage.success('正在跳转到支付页面...')

        // 创建一个新的窗口来显示支付页面
        const payWindow = window.open('', '_blank')

        // 获取支付表单HTML
        const payResponse = await fetch(payUrl, {
          method: 'GET'
        })
        const payHtml = await payResponse.text()

        // 在新窗口中写入支付表单HTML
        payWindow.document.write(payHtml)
        payWindow.document.close()
      } catch (error) {
        console.error('跳转支付页面失败:', error)
        ElMessage.error('跳转支付页面失败')
      }
    }

    // 模拟支付成功（用于测试）
    const simulatePaySuccess = async (order) => {
      try {
        await ElMessageBox.confirm('确定要模拟支付成功吗？这将更新订单状态为已支付。', '模拟支付', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })

        // 调用测试回调接口
        const response = await fetch('http://localhost:8082/api/alipay/test-callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            orderNo: order.order_no,
            tradeStatus: 'TRADE_SUCCESS'
          })
        })

        const result = await response.json()

        if (result.success) {
          ElMessage.success('支付状态更新成功！')
          // 重新加载订单列表和统计
          loadOrders()
          loadStatistics()
        } else {
          ElMessage.error(result.message || '支付状态更新失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('模拟支付失败:', error)
          ElMessage.error('模拟支付失败')
        }
      }
    }

    // 查询支付状态（调用支付宝API查询真实状态）
    const queryPayStatus = async (order) => {
      try {
        ElMessage.info('正在查询支付状态...')

        // 调用查询支付状态接口
        const response = await fetch('http://localhost:8082/api/alipay/query-pay-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            orderNo: order.order_no
          })
        })

        const result = await response.json()

        if (result.success) {
          if (result.alreadyPaid) {
            ElMessage.info('订单已经是支付状态')
          } else if (result.updated) {
            ElMessage.success(`支付状态查询成功！订单已支付（${result.tradeStatus}）`)
            // 重新加载订单列表和统计
            loadOrders()
            loadStatistics()
          } else {
            ElMessage.warning(`订单尚未支付（${result.tradeStatus || '未知状态'}）`)
          }
        } else {
          ElMessage.error(result.message || '查询支付状态失败')
        }
      } catch (error) {
        console.error('查询支付状态失败:', error)
        ElMessage.error('查询支付状态失败')
      }
    }

    // 强制确认支付状态（仅用于开发测试）
    const forceConfirmPay = async (order) => {
      try {
        await ElMessageBox.confirm(
          '这是开发测试功能，会强制将订单标记为已支付状态。确定要继续吗？',
          '强制确认支付（开发测试）',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        ElMessage.info('正在强制确认支付状态...')

        // 调用强制确认支付状态接口
        const response = await fetch('http://localhost:8082/api/alipay/force-confirm-pay', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            orderNo: order.order_no
          })
        })

        const result = await response.json()

        if (result.success) {
          if (result.alreadyPaid) {
            ElMessage.info('订单已经是支付状态')
          } else if (result.updated) {
            ElMessage.success('支付状态强制确认成功！（开发测试）')
            // 重新加载订单列表和统计
            loadOrders()
            loadStatistics()
          }
        } else {
          ElMessage.error(result.message || '强制确认失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('强制确认支付状态失败:', error)
          ElMessage.error('强制确认失败')
        }
      }
    }

    // 取消订单
    const cancelOrder = async (orderId) => {
      try {
        await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }

        const response = await ordersAPI.cancelOrder(orderId, userId)
        
        if (response.success) {
          ElMessage.success('订单取消成功')
          loadOrders() // 重新加载订单列表
          loadStatistics() // 重新加载统计
        } else {
          ElMessage.error(response.message || '取消订单失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消订单失败:', error)
          ElMessage.error('取消订单失败')
        }
      }
    }

    // 确认收货
    const confirmOrder = async (orderId) => {
      try {
        await ElMessageBox.confirm('确定要确认收货吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })

        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }

        const response = await ordersAPI.confirmOrder(orderId, userId)
        
        if (response.success) {
          ElMessage.success('确认收货成功')
          loadOrders() // 重新加载订单列表
          loadStatistics() // 重新加载统计
        } else {
          ElMessage.error(response.message || '确认收货失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('确认收货失败:', error)
          ElMessage.error('确认收货失败')
        }
      }
    }

    // 获取状态类型
    const getStatusType = (status) => {
      const statusTypes = {
        0: 'warning',  // 待支付
        1: 'info',     // 已支付
        2: 'primary',  // 已发货
        3: 'success',  // 已完成
        4: 'danger'    // 已取消
      }
      return statusTypes[status] || 'info'
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const statusTexts = {
        0: '待支付',
        1: '已支付',
        2: '已发货',
        3: '已完成',
        4: '已取消'
      }
      return statusTexts[status] || '未知状态'
    }

    // 格式化时间
    const formatTime = (timeStr) => {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN')
    }

    // 页面加载时获取数据
    onMounted(async () => {
      // 直接加载订单数据，不自动检查支付状态
      loadOrders()
      loadStatistics()

      // 检查是否有支付成功的参数
      const urlParams = new URLSearchParams(window.location.search)
      const paySuccess = urlParams.get('paySuccess')
      const orderNo = urlParams.get('orderNo')

      if (paySuccess === 'true' && orderNo) {
        ElMessage.success(`订单 ${orderNo} 支付成功！`)
        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname)
      } else if (paySuccess === 'false') {
        ElMessage.error('支付失败，请重试')
        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname)
      }
    })

    return {
      loading,
      orders,
      statistics,
      goBack,
      goToMall,
      loadOrders,
      viewOrderDetail,
      payOrder,
      simulatePaySuccess,
      queryPayStatus,
      forceConfirmPay,
      cancelOrder,
      confirmOrder,
      getStatusType,
      getStatusText,
      formatTime
    }
  }
}
</script>

<style scoped>
.orders-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  background: white;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.back-btn {
  margin-right: 16px;
  font-size: 16px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.order-stats {
  background: white;
  margin: 16px;
  padding: 20px;
  border-radius: 8px;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.orders-list {
  padding: 0 16px 16px;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

.loading .el-icon {
  font-size: 24px;
  margin-right: 8px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  color: #666;
}

.empty-state .el-icon {
  font-size: 64px;
  color: #ddd;
  margin-bottom: 16px;
}

.order-card {
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.order-no {
  font-weight: bold;
  color: #333;
}

.order-time {
  font-size: 12px;
  color: #999;
}

.order-content {
  margin-bottom: 16px;
}

.order-amount {
  margin-bottom: 8px;
}

.amount-label {
  color: #666;
}

.amount-value {
  font-size: 18px;
  font-weight: bold;
  color: #e6a23c;
}

.order-items {
  font-size: 14px;
  color: #666;
}

.order-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .order-stats {
    margin: 8px;
    padding: 16px;
  }
  
  .orders-list {
    padding: 0 8px 8px;
  }
  
  .order-card {
    margin-bottom: 8px;
    padding: 12px;
  }
  
  .order-actions {
    flex-wrap: wrap;
  }
}
</style>
