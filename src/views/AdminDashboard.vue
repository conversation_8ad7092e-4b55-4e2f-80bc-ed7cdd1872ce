<template>
  <div class="admin-dashboard">
    <!-- 顶部导航栏 -->
    <header class="admin-header">
      <div class="header-content">
        <div class="logo-section">
          <el-icon class="logo-icon"><Management /></el-icon>
          <h1 class="system-title">商城后台管理系统</h1>
        </div>
        
        <div class="header-actions">
          <div class="admin-info">
            <el-avatar :size="40" class="admin-avatar">
              <el-icon><UserFilled /></el-icon>
            </el-avatar>
            <span class="admin-name">{{ adminInfo.username }}</span>
          </div>
          
          <el-dropdown @command="handleCommand" class="admin-dropdown">
            <el-button type="primary" class="dropdown-btn">
              <el-icon><Setting /></el-icon>
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="admin-main">
      <!-- 左侧菜单 -->
      <aside class="admin-sidebar">
        <el-menu
          :default-active="activeMenu"
          class="admin-menu"
          @select="handleMenuSelect"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <el-menu-item index="dashboard" class="menu-item">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>
          
          <el-menu-item index="categories" class="menu-item">
            <el-icon><Grid /></el-icon>
            <span>分类管理</span>
          </el-menu-item>
          
          <el-menu-item index="products" class="menu-item">
            <el-icon><Goods /></el-icon>
            <span>商品管理</span>
          </el-menu-item>
          
          <el-menu-item index="orders" class="menu-item">
            <el-icon><Document /></el-icon>
            <span>订单管理</span>
          </el-menu-item>
        </el-menu>
      </aside>

      <!-- 右侧内容区域 -->
      <main class="admin-content">
        <div class="content-header">
          <div class="breadcrumb-section">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>管理后台</el-breadcrumb-item>
              <el-breadcrumb-item>{{ getCurrentPageTitle() }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
        </div>
        
        <div class="content-body">
          <!-- 首页内容 -->
          <div v-if="activeMenu === 'dashboard'" class="dashboard-content">
            <div class="stats-cards">
              <div class="stat-card">
                <div class="stat-icon products-icon">
                  <el-icon><Goods /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>商品总数</h3>
                  <p class="stat-number">{{ stats.totalProducts }}</p>
                </div>
              </div>
              
              <div class="stat-card">
                <div class="stat-icon categories-icon">
                  <el-icon><Grid /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>分类总数</h3>
                  <p class="stat-number">{{ stats.totalCategories }}</p>
                </div>
              </div>
              
              <div class="stat-card">
                <div class="stat-icon orders-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>订单总数</h3>
                  <p class="stat-number">{{ stats.totalOrders }}</p>
                </div>
              </div>
              
              <div class="stat-card">
                <div class="stat-icon users-icon">
                  <el-icon><User /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>用户总数</h3>
                  <p class="stat-number">{{ stats.totalUsers }}</p>
                </div>
              </div>
            </div>
            
            <div class="dashboard-charts">
              <div class="chart-card">
                <h3>系统概览</h3>
                <p>欢迎使用商品管理系统！</p>
                <p>请从左侧菜单选择要管理的功能模块。</p>
              </div>
            </div>
          </div>
          
          <!-- 分类管理内容 -->
          <div v-else-if="activeMenu === 'categories'" class="categories-content">
            <CategoryManagement />
          </div>
          
          <!-- 商品管理内容 -->
          <div v-else-if="activeMenu === 'products'" class="products-content">
            <ProductManagement />
          </div>
          
          <!-- 订单管理内容 -->
          <div v-else-if="activeMenu === 'orders'" class="orders-content">
            <OrderManagement />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Management,
  UserFilled,
  Setting,
  ArrowDown,
  House,
  Grid,
  Goods,
  Document,
  User,
  Plus,
  Refresh
} from '@element-plus/icons-vue'
import CategoryManagement from '@/components/CategoryManagement.vue'
import ProductManagement from '@/components/ProductManagement.vue'
import OrderManagement from '@/components/OrderManagement.vue'

export default {
  name: 'AdminDashboard',
  components: {
    Management,
    UserFilled,
    Setting,
    ArrowDown,
    House,
    Grid,
    Goods,
    Document,
    User,
    Plus,
    Refresh,
    CategoryManagement,
    ProductManagement,
    OrderManagement
  },
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const activeMenu = ref('dashboard')
    const adminInfo = reactive({
      username: '管理员',
      avatar: ''
    })
    
    const stats = reactive({
      totalProducts: 0,
      totalCategories: 0,
      totalOrders: 0,
      totalUsers: 0
    })
    
    // 获取当前页面标题
    const getCurrentPageTitle = () => {
      const titles = {
        dashboard: '首页',
        categories: '分类管理',
        products: '商品管理',
        orders: '订单管理'
      }
      return titles[activeMenu.value] || '首页'
    }
    
    // 处理菜单选择
    const handleMenuSelect = (index) => {
      activeMenu.value = index
      console.log('选择菜单:', index)
    }
    
    // 处理顶部下拉菜单命令
    const handleCommand = (command) => {
      switch (command) {
        case 'profile':
          ElMessage.info('个人设置功能开发中...')
          break
        case 'logout':
          handleLogout()
          break
      }
    }
    
    // 退出登录
    const handleLogout = async () => {
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '退出登录', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 清除管理员登录状态
        localStorage.removeItem('adminInfo')
        localStorage.removeItem('isAdminLoggedIn')
        
        ElMessage.success('已退出登录')
        router.push('/admin/login')
      } catch (error) {
        // 用户取消退出
      }
    }
    



    
    // 加载管理员信息
    const loadAdminInfo = () => {
      try {
        const adminData = localStorage.getItem('adminInfo')
        if (adminData) {
          const admin = JSON.parse(adminData)
          adminInfo.username = admin.username || '管理员'
        }
      } catch (error) {
        console.error('加载管理员信息失败:', error)
      }
    }
    
    // 加载统计数据
    const loadStats = async () => {
      try {
        // TODO: 调用后端API获取统计数据
        // 这里先使用模拟数据
        stats.totalProducts = 25
        stats.totalCategories = 8
        stats.totalOrders = 156
        stats.totalUsers = 89
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    }
    
    // 组件挂载时执行
    onMounted(() => {
      loadAdminInfo()
      loadStats()
    })
    
    return {
      activeMenu,
      adminInfo,
      stats,
      getCurrentPageTitle,
      handleMenuSelect,
      handleCommand,
      handleLogout
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
}

/* 顶部导航栏样式 */
.admin-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 64px;
}

.logo-section {
  display: flex;
  align-items: center;
  color: white;
}

.logo-icon {
  font-size: 32px;
  margin-right: 12px;
}

.system-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.admin-avatar {
  background: rgba(255, 255, 255, 0.2);
}

.admin-name {
  font-size: 14px;
  font-weight: 500;
}

.dropdown-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.dropdown-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 主要内容区域样式 */
.admin-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧菜单样式 */
.admin-sidebar {
  width: 240px;
  background-color: #304156;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.admin-menu {
  border: none;
  height: 100%;
}

.menu-item {
  height: 56px;
  line-height: 56px;
  margin: 0 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.menu-item:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.menu-item.is-active {
  background-color: #409EFF;
  color: white;
}

.menu-item .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* 右侧内容区域样式 */
.admin-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e8eaec;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.breadcrumb-section .el-breadcrumb {
  font-size: 14px;
}

.content-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* 首页统计卡片样式 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.products-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.categories-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.orders-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.users-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-number {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

/* 图表卡片样式 */
.dashboard-charts {
  display: grid;
  gap: 20px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.chart-card h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.chart-card p {
  margin: 8px 0;
  color: #666;
  line-height: 1.6;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
  font-weight: 600;
}

/* 内容占位符样式 */
.content-placeholder {
  background: white;
  border-radius: 12px;
  padding: 60px 24px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.content-placeholder p {
  margin: 0;
  font-size: 16px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-main {
    flex-direction: column;
  }

  .admin-sidebar {
    width: 100%;
    height: auto;
  }

  .admin-menu {
    display: flex;
    overflow-x: auto;
  }

  .menu-item {
    min-width: 120px;
    margin: 0 4px;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-content {
    padding: 0 16px;
  }

  .content-body {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .logo-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .system-title {
    font-size: 16px;
  }

  .admin-info {
    flex-direction: column;
    gap: 4px;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
}
</style>
