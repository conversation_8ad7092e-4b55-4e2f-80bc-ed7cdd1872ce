<template>
  <div class="admin-login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button 
          type="info" 
          :icon="ArrowLeft" 
          circle 
          @click="goBack"
          class="back-btn"
        />
      </div>
      
      <!-- 登录卡片 -->
      <el-card class="login-card" shadow="always">
        <div class="login-header">
          <div class="icon-wrapper">
            <el-icon class="login-icon">
              <UserFilled />
            </el-icon>
          </div>
          <h2 class="login-title">管理员登录</h2>
          <p class="login-subtitle">系统管理员专用登录入口</p>
        </div>
        
        <!-- 登录表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入管理员账号"
              :prefix-icon="User"
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入管理员密码"
              :prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="captcha" v-if="showCaptcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="请输入验证码"
                :prefix-icon="Key"
                clearable
                style="flex: 1; margin-right: 10px;"
              />
              <div class="captcha-code" @click="refreshCaptcha">
                {{ captchaCode }}
              </div>
            </div>
          </el-form-item>
          
          <el-form-item>
            <div class="form-options">
              <el-checkbox v-model="loginForm.remember">记住登录状态</el-checkbox>
              <el-link type="primary" :underline="false">忘记密码？</el-link>
            </div>
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="danger"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              <el-icon v-if="!loading"><Lock /></el-icon>
              {{ loading ? '登录中...' : '管理员登录' }}
            </el-button>
          </el-form-item>
        </el-form>
        
        <!-- 安全提示 -->
        <div class="security-notice">
          <el-alert
            title="安全提示"
            description="管理员账号具有系统最高权限，请妥善保管账号密码，不要在公共场所登录。"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, User, Lock, UserFilled, Key } from '@element-plus/icons-vue'
import { adminAPI } from '@/api/user'

export default {
  name: 'AdminLogin',
  components: {
    UserFilled
  },
  setup() {
    const router = useRouter()
    const loginFormRef = ref()
    const loading = ref(false)
    const showCaptcha = ref(true)
    const captchaCode = ref('')
    
    // 登录表单数据
    const loginForm = reactive({
      username: '',
      password: '',
      captcha: '',
      remember: false
    })
    
    // 表单验证规则
    const loginRules = {
      username: [
        { required: true, message: '请输入管理员账号', trigger: 'blur' },
        { min: 3, max: 20, message: '账号长度在 3 到 20 个字符', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入管理员密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
      ],
      captcha: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { len: 4, message: '验证码为4位字符', trigger: 'blur' }
      ]
    }
    
    // 生成验证码
    const generateCaptcha = () => {
      const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
      let result = ''
      for (let i = 0; i < 4; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return result
    }
    
    // 刷新验证码
    const refreshCaptcha = () => {
      captchaCode.value = generateCaptcha()
    }
    
    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }
    
    // 处理登录
    const handleLogin = async () => {
      if (!loginFormRef.value) return
      
      try {
        const valid = await loginFormRef.value.validate()
        if (valid) {
          // 验证验证码
          if (loginForm.captcha.toLowerCase() !== captchaCode.value.toLowerCase()) {
            ElMessage.error('验证码错误')
            refreshCaptcha()
            loginForm.captcha = ''
            return
          }
          
          loading.value = true

          try {
            // 调用管理员登录API
            const response = await adminAPI.login({
              username: loginForm.username,
              password: loginForm.password
            })

            if (response.success) {
              ElMessage.success(response.message || '管理员登录成功！')

              // 保存管理员信息到localStorage
              localStorage.setItem('adminInfo', JSON.stringify(response.data))
              localStorage.setItem('isAdminLoggedIn', 'true')

              // 登录成功后跳转到管理员主页
              router.push('/admin/dashboard')

              console.log('管理员登录成功，管理员信息:', response.data)
            } else {
              ElMessage.error(response.message || '管理员登录失败')
              refreshCaptcha()
              loginForm.captcha = ''
            }
            
          } catch (error) {
            console.error('管理员登录请求失败:', error)
            ElMessage.error(error.message || '登录失败，请检查网络连接')
            refreshCaptcha()
            loginForm.captcha = ''
          } finally {
            loading.value = false
          }
        }
      } catch (error) {
        console.log('表单验证失败:', error)
      }
    }
    
    // 组件挂载时生成验证码
    onMounted(() => {
      refreshCaptcha()
    })
    
    return {
      loginFormRef,
      loginForm,
      loginRules,
      loading,
      showCaptcha,
      captchaCode,
      goBack,
      handleLogin,
      refreshCaptcha,
      ArrowLeft,
      User,
      Lock,
      UserFilled,
      Key
    }
  }
}
</script>

<style scoped>
/* 主容器 */
.admin-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 20px;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.3;
  }
}

/* 主要内容 */
.main-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 450px;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: -60px;
  left: 0;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateX(-5px);
}

/* 登录卡片 */
.login-card {
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.login-card :deep(.el-card__body) {
  padding: 40px;
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.icon-wrapper {
  margin-bottom: 20px;
}

.login-icon {
  font-size: 4rem;
  color: #DC143C;
}

.login-title {
  font-size: 2rem;
  font-weight: 600;
  color: #8B0000;
  margin-bottom: 10px;
}

.login-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

/* 登录表单 */
.login-form {
  margin-top: 30px;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.login-form :deep(.el-input__inner) {
  border-radius: 10px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
}

.login-form :deep(.el-input__inner:focus) {
  border-color: #DC143C;
  box-shadow: 0 0 0 2px rgba(220, 20, 60, 0.2);
}

/* 验证码容器 */
.captcha-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.captcha-code {
  width: 100px;
  height: 40px;
  background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
  border: 2px solid #ddd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
}

.captcha-code:hover {
  background: linear-gradient(45deg, #e0e0e0, #d0d0d0);
  border-color: #DC143C;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 25px;
  background: linear-gradient(45deg, #DC143C, #B22222);
  border: none;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: linear-gradient(45deg, #B22222, #8B0000);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(220, 20, 60, 0.3);
}

/* 安全提示 */
.security-notice {
  margin-top: 20px;
}

.security-notice :deep(.el-alert) {
  border-radius: 10px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card :deep(.el-card__body) {
    padding: 30px 20px;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
  
  .back-button {
    top: -50px;
  }
  
  .captcha-code {
    width: 80px;
    height: 35px;
    font-size: 16px;
  }
}
</style>
