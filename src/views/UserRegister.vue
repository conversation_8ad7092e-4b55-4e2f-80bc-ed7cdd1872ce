<template>
  <div class="user-register-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button 
          type="info" 
          :icon="ArrowLeft" 
          circle 
          @click="goBack"
          class="back-btn"
        />
      </div>
      
      <!-- 注册卡片 -->
      <el-card class="register-card" shadow="always">
        <div class="register-header">
          <div class="icon-wrapper">
            <el-icon class="register-icon">
              <UserFilled />
            </el-icon>
          </div>
          <h2 class="register-title">用户注册</h2>
          <p class="register-subtitle">创建您的新账户</p>
        </div>
        
        <!-- 注册表单 -->
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          class="register-form"
          size="large"
        >
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="请输入用户名"
              :prefix-icon="User"
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="email">
            <el-input
              v-model="registerForm.email"
              placeholder="请输入邮箱"
              :prefix-icon="Message"
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="phone">
            <el-input
              v-model="registerForm.phone"
              placeholder="请输入手机号"
              :prefix-icon="Phone"
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="请确认密码"
              :prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>
          
          <el-form-item prop="agreement">
            <el-checkbox v-model="registerForm.agreement">
              我已阅读并同意
              <el-link type="primary" :underline="false">《用户协议》</el-link>
              和
              <el-link type="primary" :underline="false">《隐私政策》</el-link>
            </el-checkbox>
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="success"
              class="register-button"
              :loading="loading"
              @click="handleRegister"
            >
              <el-icon v-if="!loading"><UserFilled /></el-icon>
              {{ loading ? '注册中...' : '立即注册' }}
            </el-button>
          </el-form-item>
        </el-form>
        
        <!-- 登录链接 -->
        <div class="login-link">
          <span>已有账户？</span>
          <el-link 
            type="primary" 
            :underline="false" 
            @click="goToLogin"
            class="login-btn"
          >
            立即登录
          </el-link>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, User, UserFilled, Lock, Message, Phone } from '@element-plus/icons-vue'
import { userAPI } from '@/api/user'

export default {
  name: 'UserRegister',
  components: {
    UserFilled
  },
  setup() {
    const router = useRouter()
    const registerFormRef = ref()
    const loading = ref(false)
    
    // 注册表单数据
    const registerForm = reactive({
      username: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      agreement: false
    })
    
    // 自定义验证函数
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== registerForm.password) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    
    const validateAgreement = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请阅读并同意用户协议'))
      } else {
        callback()
      }
    }
    
    // 表单验证规则
    const registerRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
        { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, validator: validateConfirmPassword, trigger: 'blur' }
      ],
      agreement: [
        { required: true, validator: validateAgreement, trigger: 'change' }
      ]
    }
    
    // 返回上一页
    const goBack = () => {
      router.go(-1)
    }
    
    // 跳转到登录页面
    const goToLogin = () => {
      router.push('/user/login')
    }
    
    // 处理注册
    const handleRegister = async () => {
      if (!registerFormRef.value) return

      try {
        const valid = await registerFormRef.value.validate()
        if (valid) {
          loading.value = true

          try {
            // 调用注册API
            const response = await userAPI.register({
              username: registerForm.username,
              email: registerForm.email,
              phone: registerForm.phone,
              password: registerForm.password
            })

            if (response.success) {
              ElMessage.success(response.message || '注册成功！请登录您的账户')

              // 注册成功后跳转到登录页面
              router.push('/user/login')
            } else {
              ElMessage.error(response.message || '注册失败')
            }
          } catch (error) {
            console.error('注册请求失败:', error)
            ElMessage.error(error.message || '注册失败，请检查网络连接')
          } finally {
            loading.value = false
          }
        }
      } catch (error) {
        console.log('表单验证失败:', error)
      }
    }
    
    return {
      registerFormRef,
      registerForm,
      registerRules,
      loading,
      goBack,
      goToLogin,
      handleRegister,
      ArrowLeft,
      User,
      UserFilled,
      Lock,
      Message,
      Phone
    }
  }
}
</script>

<style scoped>
/* 主容器 */
.user-register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 50%, #6b8e5a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 20px;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.3;
  }
}

/* 主要内容 */
.main-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 450px;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: -60px;
  left: 0;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateX(-5px);
}

/* 注册卡片 */
.register-card {
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.register-card :deep(.el-card__body) {
  padding: 40px;
}

/* 注册头部 */
.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.icon-wrapper {
  margin-bottom: 20px;
}

.register-icon {
  font-size: 4rem;
  color: #67c23a;
}

.register-title {
  font-size: 2rem;
  font-weight: 600;
  color: #2d5a27;
  margin-bottom: 10px;
}

.register-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

/* 注册表单 */
.register-form {
  margin-top: 30px;
}

.register-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.register-form :deep(.el-input__inner) {
  border-radius: 10px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
}

.register-form :deep(.el-input__inner:focus) {
  border-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

/* 注册按钮 */
.register-button {
  width: 100%;
  height: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 25px;
  background: linear-gradient(45deg, #67c23a, #85ce61);
  border: none;
  transition: all 0.3s ease;
}

.register-button:hover {
  background: linear-gradient(45deg, #529b2e, #67c23a);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(103, 194, 58, 0.3);
}

/* 登录链接 */
.login-link {
  text-align: center;
  margin-top: 20px;
  color: #666;
}

.login-btn {
  margin-left: 8px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .register-card :deep(.el-card__body) {
    padding: 30px 20px;
  }
  
  .register-title {
    font-size: 1.5rem;
  }
  
  .back-button {
    top: -50px;
  }
}
</style>
