import request from './user'

// 订单相关API
const ordersAPI = {
  // 获取用户订单列表
  getUserOrders(userId) {
    return request({
      url: `/orders/list/${userId}`,
      method: 'get'
    })
  },

  // 获取订单详情
  getOrderDetail(orderId) {
    return request({
      url: `/orders/detail/${orderId}`,
      method: 'get'
    })
  },

  // 获取订单统计
  getOrderStatistics(userId) {
    return request({
      url: `/orders/statistics/${userId}`,
      method: 'get'
    })
  },

  // 从购物车创建订单
  createOrderFromCart(data) {
    return request({
      url: '/orders/create-from-cart',
      method: 'post',
      data
    })
  },

  // 直接购买创建订单
  createOrderDirect(data) {
    return request({
      url: '/orders/create-direct',
      method: 'post',
      data
    })
  },

  // 取消订单
  cancelOrder(orderId) {
    return request({
      url: `/orders/cancel/${orderId}`,
      method: 'put'
    })
  },

  // 确认收货
  confirmOrder(orderId, userId) {
    return request({
      url: `/orders/confirm/${orderId}`,
      method: 'put',
      data: {
        userId
      }
    })
  }
}

export default ordersAPI
