<template>
  <div class="order-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>订单管理</h2>
        <p class="page-description">管理所有用户订单，处理发货和取消操作</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="loadOrders" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新订单
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon total-icon">
          <el-icon><Document /></el-icon>
        </div>
        <div class="stat-info">
          <h3>订单总数</h3>
          <p class="stat-number">{{ statistics.total_orders || 0 }}</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon pending-icon">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="stat-info">
          <h3>待支付</h3>
          <p class="stat-number">{{ statistics.pending_payment || 0 }}</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon paid-icon">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stat-info">
          <h3>已支付</h3>
          <p class="stat-number">{{ statistics.paid || 0 }}</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon shipped-icon">
          <el-icon><Van /></el-icon>
        </div>
        <div class="stat-info">
          <h3>已发货</h3>
          <p class="stat-number">{{ statistics.shipped || 0 }}</p>
        </div>
      </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar">
      <div class="filter-left">
        <el-select v-model="selectedStatus" placeholder="选择订单状态" clearable @change="handleStatusFilter">
          <el-option label="全部状态" value="" />
          <el-option label="待支付" :value="0" />
          <el-option label="已支付" :value="1" />
          <el-option label="已发货" :value="2" />
          <el-option label="已完成" :value="3" />
          <el-option label="已取消" :value="4" />
        </el-select>
        
        <el-input
          v-model="searchKeyword"
          placeholder="搜索订单号或用户名"
          clearable
          @input="debouncedSearch"
          @clear="handleSearch"
          style="width: 250px; margin-left: 12px;"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="orders-table-container">
      <el-table
        :data="filteredOrders"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'create_time', order: 'descending' }"
      >
        <el-table-column prop="id" label="订单ID" width="80" />
        
        <el-table-column prop="order_no" label="订单号" width="180">
          <template #default="scope">
            <el-text class="order-no">{{ scope.row.order_no }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column prop="user_name" label="用户" width="120">
          <template #default="scope">
            <div class="user-info">
              <el-avatar :size="32" class="user-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span>{{ scope.row.user_name || '未知用户' }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="total_amount" label="订单金额" width="120">
          <template #default="scope">
            <el-text class="amount">¥{{ scope.row.total_amount }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column prop="item_count" label="商品数量" width="100">
          <template #default="scope">
            <el-tag size="small">{{ scope.row.item_count }}件</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="订单状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="create_time" label="创建时间" width="180">
          <template #default="scope">
            <div class="time-info">
              <div>{{ formatDateTime(scope.row.create_time) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="240" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <!-- 发货按钮 - 仅已支付订单显示 -->
              <el-button
                v-if="scope.row.status === 1"
                type="primary"
                size="small"
                @click="handleShipOrder(scope.row)"
                class="action-btn"
              >
                <el-icon><Van /></el-icon>
                发货
              </el-button>

              <!-- 取消按钮 - 待支付和已支付订单显示 -->
              <el-button
                v-if="scope.row.status === 0 || scope.row.status === 1"
                type="danger"
                size="small"
                @click="handleCancelOrder(scope.row)"
                class="action-btn"
              >
                <el-icon><Close /></el-icon>
                取消
              </el-button>

              <!-- 详情按钮 - 所有订单都显示 -->
              <el-button
                type="info"
                size="small"
                @click="viewOrderDetail(scope.row)"
                class="action-btn"
              >
                <el-icon><View /></el-icon>
                详情
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 发货对话框 -->
    <el-dialog
      v-model="shipDialogVisible"
      title="订单发货"
      width="500px"
      :before-close="handleShipDialogClose"
    >
      <div class="ship-form">
        <div class="order-info">
          <h4>订单信息</h4>
          <p><strong>订单号：</strong>{{ currentOrder?.order_no }}</p>
          <p><strong>用户：</strong>{{ currentOrder?.user_name }}</p>
          <p><strong>金额：</strong>¥{{ currentOrder?.total_amount }}</p>
        </div>
        
        <el-form :model="shipForm" label-width="80px">
          <el-form-item label="发货备注">
            <el-input
              v-model="shipForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入发货备注（可选）"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shipDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmShipOrder" :loading="shipLoading">
            确认发货
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="订单详情"
      width="800px"
      :before-close="handleDetailDialogClose"
      destroy-on-close
    >
      <div v-if="orderDetail" class="order-detail">
        <div class="detail-header">
          <div class="detail-row">
            <span class="label">订单号：</span>
            <span class="value">{{ orderDetail.orderNo }}</span>
          </div>
          <div class="detail-row">
            <span class="label">订单状态：</span>
            <el-tag :type="getStatusType(orderDetail.status)">
              {{ getStatusText(orderDetail.status) }}
            </el-tag>
          </div>
          <div class="detail-row">
            <span class="label">订单金额：</span>
            <span class="value amount">¥{{ orderDetail.totalAmount }}</span>
          </div>
          <div class="detail-row">
            <span class="label">创建时间：</span>
            <span class="value">{{ formatDateTime(orderDetail.createTime) }}</span>
          </div>
          <div class="detail-row" v-if="orderDetail.payTime">
            <span class="label">支付时间：</span>
            <span class="value">{{ formatDateTime(orderDetail.payTime) }}</span>
          </div>
        </div>

        <div class="detail-items">
          <h4>商品清单</h4>
          <div class="items-container">
            <el-table
              :data="orderDetail.items"
              border
              style="width: 100%"
              :height="300"
              v-loading="detailLoading"
            >
              <el-table-column label="商品图片" width="80" fixed="left">
                <template #default="scope">
                  <div class="image-container">
                    <el-image
                      :src="getImageUrl(scope.row.imageUrl)"
                      :alt="scope.row.productName"
                      class="product-image"
                      fit="cover"
                      :preview-src-list="[getImageUrl(scope.row.imageUrl)]"
                      :initial-index="0"
                      preview-teleported
                      loading="lazy"
                    >
                      <template #error>
                        <div class="image-error">
                          <el-icon><Picture /></el-icon>
                        </div>
                      </template>
                    </el-image>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="productName" label="商品名称" min-width="150" />
              <el-table-column prop="price" label="单价" width="100">
                <template #default="scope">
                  ¥{{ scope.row.price }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="subtotal" label="小计" width="100">
                <template #default="scope">
                  ¥{{ scope.row.subtotal }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <div v-else class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ordersAPI } from '@/api/user.js'
import {
  Refresh,
  Document,
  Clock,
  Money,
  Van,
  Search,
  User,
  Close,
  View,
  Picture
} from '@element-plus/icons-vue'

export default {
  name: 'OrderManagement',
  components: {
    Refresh,
    Document,
    Clock,
    Money,
    Van,
    Search,
    User,
    Close,
    View,
    Picture
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const orders = ref([])
    const statistics = ref({})
    const selectedStatus = ref('')
    const searchKeyword = ref('')
    
    // 对话框相关
    const shipDialogVisible = ref(false)
    const detailDialogVisible = ref(false)
    const shipLoading = ref(false)
    const detailLoading = ref(false)
    const currentOrder = ref(null)
    const orderDetail = ref(null)
    
    // 发货表单
    const shipForm = reactive({
      remark: ''
    })

    // 计算属性 - 直接返回服务端筛选后的订单
    const filteredOrders = computed(() => {
      return orders.value
    })

    // 方法
    const loadOrders = async (showMessage = false) => {
      loading.value = true
      try {
        console.log('开始加载订单列表...')
        console.log('搜索关键词:', searchKeyword.value)
        console.log('选择状态:', selectedStatus.value)
        console.log('ordersAPI对象:', ordersAPI)

        let response

        // 如果有搜索关键词或状态筛选，使用搜索API
        if (searchKeyword.value.trim() || selectedStatus.value !== '') {
          console.log('使用搜索API')
          response = await ordersAPI.searchOrdersForAdmin(searchKeyword.value, selectedStatus.value)
        } else {
          // 否则获取所有订单
          console.log('使用获取所有订单API')
          response = await ordersAPI.getAllOrdersForAdmin()
        }

        console.log('API响应:', response)

        // 正确处理响应格式
        // 如果response有data属性且data是对象（包含success字段），使用response.data
        // 如果response有data属性但data是数组，说明response本身就是完整响应
        let responseData
        if (response.data && typeof response.data === 'object' && Object.prototype.hasOwnProperty.call(response.data, 'success')) {
          // axios格式：response.data包含{success, data, message}
          responseData = response.data
        } else if (Object.prototype.hasOwnProperty.call(response, 'success')) {
          // 直接格式：response本身包含{success, data, message}
          responseData = response
        } else {
          // 异常情况，尝试使用response
          responseData = response
        }

        console.log('处理后的responseData:', responseData)
        console.log('responseData.success:', responseData.success)
        console.log('responseData.data:', responseData.data)

        if (responseData && responseData.success === true) {
          orders.value = responseData.data
          console.log('订单数据加载成功，数量:', orders.value.length)
          if (showMessage) {
            if (searchKeyword.value.trim() || selectedStatus.value !== '') {
              ElMessage.success(`搜索到 ${orders.value.length} 个订单`)
            } else {
              ElMessage.success('订单列表加载成功')
            }
          }
        } else {
          console.error('API响应格式错误:', response)
          console.error('处理后的responseData:', responseData)
          console.error('条件判断失败原因:', {
            'responseData存在': !!responseData,
            'responseData.success值': responseData ? responseData.success : 'responseData为空',
            'responseData.success类型': responseData ? typeof responseData.success : 'N/A',
            'responseData.success === true': responseData ? responseData.success === true : false
          })
          ElMessage.error((responseData && responseData.message) || '获取订单列表失败')
        }
      } catch (error) {
        console.error('获取订单列表失败 - 详细错误:', error)
        console.error('错误类型:', error.name)
        console.error('错误消息:', error.message)
        console.error('错误堆栈:', error.stack)
        ElMessage.error(`获取订单列表失败: ${error.message}`)
      } finally {
        loading.value = false
      }
    }

    const loadStatistics = async () => {
      try {
        const response = await ordersAPI.getAdminOrderStatistics()

        // 检查响应格式
        const responseData = response.data || response

        if (responseData.success) {
          statistics.value = responseData.data
        }
      } catch (error) {
        console.error('获取订单统计失败:', error)
      }
    }

    const handleStatusFilter = () => {
      console.log('状态筛选:', selectedStatus.value)
      loadOrders(true) // 重新加载订单，显示成功消息
    }

    const handleSearch = () => {
      console.log('搜索关键词:', searchKeyword.value)
      loadOrders(true) // 重新加载订单，显示成功消息
    }

    // 添加防抖搜索
    let searchTimeout = null
    const debouncedSearch = () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }
      searchTimeout = setTimeout(() => {
        handleSearch()
      }, 500) // 500ms防抖
    }

    const handleShipOrder = (order) => {
      currentOrder.value = order
      shipForm.remark = ''
      shipDialogVisible.value = true
    }

    const handleCancelOrder = async (order) => {
      try {
        await ElMessageBox.confirm(
          `确定要取消订单 ${order.order_no} 吗？`,
          '取消订单',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        const response = await ordersAPI.updateOrderStatusByAdmin(order.id, 4, '管理员取消订单')

        // 检查响应格式
        const responseData = response.data || response

        if (responseData.success) {
          ElMessage.success('订单取消成功')
          loadOrders()
          loadStatistics()
        } else {
          ElMessage.error(responseData.message || '订单取消失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消订单失败:', error)
          ElMessage.error('取消订单失败')
        }
      }
    }

    const confirmShipOrder = async () => {
      shipLoading.value = true
      try {
        const response = await ordersAPI.updateOrderStatusByAdmin(
          currentOrder.value.id,
          2,
          shipForm.remark || '订单已发货'
        )

        // 检查响应格式
        const responseData = response.data || response

        if (responseData.success) {
          ElMessage.success('订单发货成功')
          shipDialogVisible.value = false
          loadOrders()
          loadStatistics()
        } else {
          ElMessage.error(responseData.message || '订单发货失败')
        }
      } catch (error) {
        console.error('订单发货失败:', error)
        ElMessage.error('订单发货失败')
      } finally {
        shipLoading.value = false
      }
    }

    const handleShipDialogClose = () => {
      shipDialogVisible.value = false
      currentOrder.value = null
      shipForm.remark = ''
    }

    const viewOrderDetail = async (order) => {
      try {
        console.log('开始获取订单详情，订单ID:', order.id)
        detailLoading.value = true
        orderDetail.value = null
        detailDialogVisible.value = true

        const response = await ordersAPI.getOrderDetail(order.id)
        console.log('订单详情API响应:', response)

        // 正确处理响应格式
        let responseData
        if (response.data && typeof response.data === 'object' && Object.prototype.hasOwnProperty.call(response.data, 'success')) {
          // axios格式：response.data包含{success, data, message}
          responseData = response.data
        } else if (Object.prototype.hasOwnProperty.call(response, 'success')) {
          // 直接格式：response本身包含{success, data, message}
          responseData = response
        } else {
          // 异常情况，尝试使用response
          responseData = response
        }

        console.log('处理后的订单详情responseData:', responseData)
        console.log('responseData.success:', responseData.success)
        console.log('responseData.data:', responseData.data)

        if (responseData && responseData.success === true) {
          // 延迟一点时间确保对话框完全打开
          await new Promise(resolve => setTimeout(resolve, 100))
          orderDetail.value = responseData.data
          console.log('订单详情设置成功:', orderDetail.value)
        } else {
          console.error('订单详情API响应格式错误:', response)
          console.error('处理后的responseData:', responseData)
          console.error('条件判断失败原因:', {
            'responseData存在': !!responseData,
            'responseData.success值': responseData ? responseData.success : 'responseData为空',
            'responseData.success类型': responseData ? typeof responseData.success : 'N/A',
            'responseData.success === true': responseData ? responseData.success === true : false
          })
          ElMessage.error((responseData && responseData.message) || '获取订单详情失败')
          detailDialogVisible.value = false
        }
      } catch (error) {
        console.error('获取订单详情失败 - 详细错误:', error)
        console.error('错误类型:', error.name)
        console.error('错误消息:', error.message)
        console.error('错误堆栈:', error.stack)
        ElMessage.error(`获取订单详情失败: ${error.message}`)
        detailDialogVisible.value = false
      } finally {
        detailLoading.value = false
      }
    }

    const handleDetailDialogClose = () => {
      detailDialogVisible.value = false
      orderDetail.value = null
      detailLoading.value = false
    }

    const getStatusText = (status) => {
      const statusMap = {
        0: '待支付',
        1: '已支付',
        2: '已发货',
        3: '已完成',
        4: '已取消'
      }
      return statusMap[status] || '未知状态'
    }

    const getStatusType = (status) => {
      const typeMap = {
        0: 'warning',
        1: 'success',
        2: 'primary',
        3: 'success',
        4: 'danger'
      }
      return typeMap[status] || 'info'
    }

    const formatDateTime = (dateTime) => {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const getImageUrl = (imageUrl) => {
      if (!imageUrl || imageUrl.trim() === '') {
        return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iI0NDQ0NDQyIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjI4IiBjeT0iMjgiIHI9IjMiIGZpbGw9IiNDQ0NDQ0MiLz4KPHBhdGggZD0iTTIwIDM2TDI4IDI4TDM2IDM2TDQ0IDI4VjQ0SDIwVjM2WiIgZmlsbD0iI0NDQ0NDQyIvPgo8L3N2Zz4K'
      }
      if (imageUrl.startsWith('http')) return imageUrl
      return `http://localhost:8082${imageUrl}`
    }

    // 组件挂载时执行
    onMounted(() => {
      loadOrders()
      loadStatistics()
    })

    return {
      loading,
      orders,
      statistics,
      selectedStatus,
      searchKeyword,
      filteredOrders,
      shipDialogVisible,
      detailDialogVisible,
      shipLoading,
      detailLoading,
      currentOrder,
      orderDetail,
      shipForm,
      loadOrders,
      handleStatusFilter,
      handleSearch,
      debouncedSearch,
      handleShipOrder,
      handleCancelOrder,
      confirmShipOrder,
      handleShipDialogClose,
      viewOrderDetail,
      handleDetailDialogClose,
      getStatusText,
      getStatusType,
      formatDateTime,
      getImageUrl
    }
  }
}
</script>

<style scoped>
.order-management {
  padding: 0;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 24px;
  color: #333;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片样式 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.total-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pending-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.paid-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.shipped-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-number {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

/* 筛选工具栏样式 */
.filter-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.filter-left {
  display: flex;
  align-items: center;
}

/* 订单表格样式 */
.orders-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.order-no {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #409EFF;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  background: #f0f2f5;
}

.amount {
  font-weight: 600;
  color: #E6A23C;
}

.time-info {
  font-size: 13px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
}

.action-btn {
  min-width: 60px;
  height: 28px;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.action-btn .el-icon {
  font-size: 12px;
}

/* 对话框样式 */
.ship-form {
  padding: 16px 0;
}

.order-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.order-info h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.order-info p {
  margin: 8px 0;
  color: #666;
}

.order-detail {
  padding: 16px 0;
}

.detail-header {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row .label {
  width: 100px;
  color: #666;
  font-weight: 500;
}

.detail-row .value {
  color: #333;
  font-weight: 500;
}

.detail-row .value.amount {
  color: #E6A23C;
  font-weight: 600;
  font-size: 16px;
}

.detail-items h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
}

.items-container {
  max-height: 400px;
  overflow: auto;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
}

.product-image {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  border: 1px solid #e8eaec;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background-color: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #e8eaec;
  color: #ccc;
  font-size: 20px;
}

.loading-container {
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .filter-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .filter-left {
    width: 100%;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-btn {
    width: 100%;
    min-width: auto;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-row .label {
    width: auto;
  }
}

@media (max-width: 480px) {
  .stat-card {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .order-info,
  .detail-header {
    padding: 12px;
  }
}
</style>
