<template>
  <div class="login-choice-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 标题 -->
      <div class="title-section">
        <h1 class="main-title">商品管理系统</h1>
        <p class="subtitle">Product Management System</p>
      </div>
      
      <!-- 登录选择卡片 -->
      <div class="choice-cards">
        <!-- 管理员登录卡片 -->
        <el-card 
          class="choice-card admin-card" 
          shadow="hover"
          @click="handleAdminLogin"
        >
          <div class="card-content">
            <div class="icon-wrapper">
              <el-icon class="card-icon">
                <UserFilled />
              </el-icon>
            </div>
            <h3 class="card-title">管理员登录</h3>
            <p class="card-description">
              系统管理员入口<br>
              管理商品、用户、订单等
            </p>
            <el-button 
              type="primary" 
              class="login-btn admin-btn"
              size="large"
            >
              <el-icon><Lock /></el-icon>
              管理员登录
            </el-button>
          </div>
        </el-card>
        
        <!-- 用户登录卡片 -->
        <el-card 
          class="choice-card user-card" 
          shadow="hover"
          @click="handleUserLogin"
        >
          <div class="card-content">
            <div class="icon-wrapper">
              <el-icon class="card-icon">
                <User />
              </el-icon>
            </div>
            <h3 class="card-title">用户登录</h3>
            <p class="card-description">
              普通用户入口<br>
              浏览商品、购物下单等
            </p>
            <el-button 
              type="success" 
              class="login-btn user-btn"
              size="large"
            >
              <el-icon><ShoppingCart /></el-icon>
              用户登录
            </el-button>
          </div>
        </el-card>
      </div>
      
      <!-- 底部信息 -->
      <div class="footer-info">
        <p>© 2024 商品管理系统 - 基于Vue3 + Spring Boot</p>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'

export default {
  name: 'LoginChoice',
  setup() {
    const router = useRouter()

    const handleAdminLogin = () => {
      router.push('/admin/login')
    }

    const handleUserLogin = () => {
      router.push('/user/login')
    }

    return {
      handleAdminLogin,
      handleUserLogin
    }
  }
}
</script>

<style scoped>
/* 主容器 */
.login-choice-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 50%, #6b8e5a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.3;
  }
}

/* 主要内容 */
.main-content {
  text-align: center;
  z-index: 1;
  max-width: 800px;
  width: 100%;
  padding: 0 20px;
}

/* 标题部分 */
.title-section {
  margin-bottom: 60px;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 2px;
}

.subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 300;
  letter-spacing: 1px;
}

/* 选择卡片容器 */
.choice-cards {
  display: flex;
  gap: 40px;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

/* 卡片样式 */
.choice-card {
  width: 300px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 2px solid transparent;
}

.choice-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  border-color: #4a7c59;
}

.admin-card:hover {
  border-color: #409eff;
}

.user-card:hover {
  border-color: #67c23a;
}

/* 卡片内容 */
.card-content {
  padding: 40px 30px;
  text-align: center;
}

.icon-wrapper {
  margin-bottom: 20px;
}

.card-icon {
  font-size: 4rem;
  color: #4a7c59;
  transition: all 0.3s ease;
}

.admin-card:hover .card-icon {
  color: #409eff;
  transform: scale(1.1);
}

.user-card:hover .card-icon {
  color: #67c23a;
  transform: scale(1.1);
}

.card-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d5a27;
  margin-bottom: 15px;
}

.card-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.admin-btn {
  background: linear-gradient(45deg, #409eff, #66b3ff);
  border: none;
}

.admin-btn:hover {
  background: linear-gradient(45deg, #337ecc, #409eff);
  transform: scale(1.05);
}

.user-btn {
  background: linear-gradient(45deg, #67c23a, #85ce61);
  border: none;
}

.user-btn:hover {
  background: linear-gradient(45deg, #529b2e, #67c23a);
  transform: scale(1.05);
}

/* 底部信息 */
.footer-info {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
  }
  
  .choice-cards {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }
  
  .choice-card {
    width: 280px;
  }
  
  .card-content {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
  }
  
  .choice-card {
    width: 100%;
    max-width: 280px;
  }
}
</style>
