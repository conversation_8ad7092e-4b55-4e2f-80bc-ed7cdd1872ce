package qidian.it.springboot.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import qidian.it.springboot.entity.User;
import qidian.it.springboot.service.UserService;
import qidian.it.springboot.util.MD5Util;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/api/admin")
public class AdminController {

    @Autowired
    private UserService userService;

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, String> loginData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = loginData.get("username");
            String password = loginData.get("password");
            
            // 参数验证
            if (username == null || username.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "管理员账号不能为空");
                return result;
            }
            
            if (password == null || password.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "管理员密码不能为空");
                return result;
            }
            
            // 根据用户名查找用户
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("username", username);
            User user = userService.getOne(wrapper);
            
            // 检查用户是否存在
            if (user == null) {
                result.put("success", false);
                result.put("message", "管理员账号不存在");
                return result;
            }
            
            // 检查用户是否为管理员 (status = 2)
            if (user.getStatus() != 2) {
                result.put("success", false);
                result.put("message", "该账号不是管理员账号，无权限登录管理系统");
                return result;
            }
            
            // 验证密码
            boolean passwordValid = MD5Util.checkPassword(password, user.getPassword());
            
            if (passwordValid) {
                result.put("success", true);
                result.put("message", "管理员登录成功");
                
                // 返回管理员信息（不包含密码）
                Map<String, Object> adminData = new HashMap<>();
                adminData.put("id", user.getId());
                adminData.put("username", user.getUsername());
                adminData.put("email", user.getEmail());
                adminData.put("phone", user.getPhone());
                adminData.put("createTime", user.getCreateTime());
                adminData.put("status", user.getStatus());
                adminData.put("role", "admin"); // 标识为管理员角色
                
                result.put("data", adminData);
                
                // 记录登录日志
                System.out.println("管理员登录成功: " + username + " at " + java.time.LocalDateTime.now());
                
            } else {
                result.put("success", false);
                result.put("message", "管理员密码错误");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "管理员登录失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }

    /**
     * 检查管理员权限
     */
    @GetMapping("/check-permission")
    public Map<String, Object> checkPermission(@RequestParam String username) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("username", username);
            User user = userService.getOne(wrapper);
            
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                result.put("isAdmin", false);
                return result;
            }
            
            boolean isAdmin = user.getStatus() == 2;
            result.put("success", true);
            result.put("isAdmin", isAdmin);
            result.put("message", isAdmin ? "该用户是管理员" : "该用户不是管理员");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "检查权限失败：" + e.getMessage());
            result.put("isAdmin", false);
        }
        
        return result;
    }

    /**
     * 获取管理员信息
     */
    @GetMapping("/info")
    public Map<String, Object> getAdminInfo(@RequestParam Long adminId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            User admin = userService.getById(adminId);
            
            if (admin == null) {
                result.put("success", false);
                result.put("message", "管理员不存在");
                return result;
            }
            
            if (admin.getStatus() != 2) {
                result.put("success", false);
                result.put("message", "该用户不是管理员");
                return result;
            }
            
            // 返回管理员信息（不包含密码）
            Map<String, Object> adminData = new HashMap<>();
            adminData.put("id", admin.getId());
            adminData.put("username", admin.getUsername());
            adminData.put("email", admin.getEmail());
            adminData.put("phone", admin.getPhone());
            adminData.put("createTime", admin.getCreateTime());
            adminData.put("status", admin.getStatus());
            adminData.put("role", "admin");
            
            result.put("success", true);
            result.put("data", adminData);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取管理员信息失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 管理员修改密码
     */
    @PostMapping("/change-password")
    public Map<String, Object> changePassword(@RequestBody Map<String, String> passwordData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = passwordData.get("username");
            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");
            
            // 参数验证
            if (username == null || username.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "用户名不能为空");
                return result;
            }
            
            if (oldPassword == null || oldPassword.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "原密码不能为空");
                return result;
            }
            
            if (newPassword == null || newPassword.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "新密码不能为空");
                return result;
            }
            
            if (newPassword.length() < 6) {
                result.put("success", false);
                result.put("message", "新密码长度不能少于6位");
                return result;
            }
            
            // 查找管理员
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("username", username);
            wrapper.eq("status", 2); // 确保是管理员
            User admin = userService.getOne(wrapper);
            
            if (admin == null) {
                result.put("success", false);
                result.put("message", "管理员不存在");
                return result;
            }
            
            // 验证原密码
            boolean oldPasswordValid = MD5Util.checkPassword(oldPassword, admin.getPassword());
            if (!oldPasswordValid) {
                result.put("success", false);
                result.put("message", "原密码错误");
                return result;
            }
            
            // 加密新密码
            String encryptedNewPassword = MD5Util.MD5PassWord(newPassword);
            admin.setPassword(encryptedNewPassword);
            
            // 更新密码
            boolean updated = userService.updateById(admin);
            
            if (updated) {
                result.put("success", true);
                result.put("message", "管理员密码修改成功");
                
                // 记录操作日志
                System.out.println("管理员密码修改成功: " + username + " at " + java.time.LocalDateTime.now());
            } else {
                result.put("success", false);
                result.put("message", "密码修改失败，请重试");
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "密码修改失败：" + e.getMessage());
            e.printStackTrace();
        }
        
        return result;
    }
}
