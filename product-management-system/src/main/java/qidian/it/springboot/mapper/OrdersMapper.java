package qidian.it.springboot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import qidian.it.springboot.entity.Orders;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单Mapper接口
 */
@Mapper
public interface OrdersMapper extends BaseMapper<Orders> {
    
    /**
     * 查询用户订单列表（包含订单项信息）
     */
    @Select("SELECT o.id, o.order_no, o.total_amount, o.status, o.create_time, o.pay_time, " +
            "COUNT(oi.id) as item_count " +
            "FROM orders o " +
            "LEFT JOIN order_item oi ON o.id = oi.order_id " +
            "WHERE o.user_id = #{userId} " +
            "GROUP BY o.id " +
            "ORDER BY o.create_time DESC")
    List<Map<String, Object>> selectOrdersByUserId(Long userId);
    
    /**
     * 查询订单详情（包含订单项和商品信息）
     */
    @Select("SELECT o.id as order_id, o.order_no, o.total_amount, o.status, o.create_time, o.pay_time, " +
            "oi.id as item_id, oi.quantity, oi.price, oi.subtotal, " +
            "p.id as product_id, p.name as product_name, p.description, p.image_url " +
            "FROM orders o " +
            "LEFT JOIN order_item oi ON o.id = oi.order_id " +
            "LEFT JOIN product p ON oi.product_id = p.id " +
            "WHERE o.id = #{orderId}")
    List<Map<String, Object>> selectOrderDetailById(Long orderId);
    
    /**
     * 根据订单号查询订单
     */
    @Select("SELECT * FROM orders WHERE order_no = #{orderNo}")
    Orders selectByOrderNo(String orderNo);

    /**
     * 查询用户待支付订单
     */
    @Select("SELECT * FROM orders WHERE user_id = #{userId} AND status = 0 ORDER BY create_time DESC")
    List<Map<String, Object>> selectPendingOrdersByUserId(Long userId);
    
    /**
     * 更新订单支付状态
     */
    @Update("UPDATE orders SET status = #{status}, pay_time = #{payTime} WHERE order_no = #{orderNo}")
    int updatePayStatus(String orderNo, Integer status, LocalDateTime payTime);
    
    /**
     * 统计用户订单数量
     */
    @Select("SELECT COUNT(*) FROM orders WHERE user_id = #{userId}")
    Integer countByUserId(Long userId);
    
    /**
     * 查询用户各状态订单数量
     */
    @Select("SELECT status, COUNT(*) as count FROM orders WHERE user_id = #{userId} GROUP BY status")
    List<Map<String, Object>> countOrdersByStatus(Long userId);

    // ========================================
    // 管理员订单管理查询
    // ========================================

    /**
     * 查询所有订单列表（管理员）
     */
    @Select("SELECT o.id, o.order_no, o.user_id, o.total_amount, o.status, o.create_time, o.pay_time, " +
            "COUNT(oi.id) as item_count, " +
            "u.username as user_name " +
            "FROM orders o " +
            "LEFT JOIN order_item oi ON o.id = oi.order_id " +
            "LEFT JOIN user u ON o.user_id = u.id " +
            "GROUP BY o.id " +
            "ORDER BY o.create_time DESC")
    List<Map<String, Object>> selectAllOrdersForAdmin();

    /**
     * 更新订单状态（管理员）
     */
    @Update("UPDATE orders SET status = #{status} WHERE id = #{orderId}")
    int updateOrderStatusByAdmin(Long orderId, Integer status);

    /**
     * 统计各状态订单数量（管理员）
     */
    @Select("SELECT " +
            "COUNT(*) as total_orders, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_payment, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as paid, " +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as shipped, " +
            "SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed, " +
            "SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as cancelled, " +
            "SUM(total_amount) as total_amount " +
            "FROM orders")
    Map<String, Object> selectAdminOrderStatistics();

    /**
     * 搜索订单列表（管理员）- 支持订单号和用户名搜索
     */
    @Select("SELECT o.id, o.order_no, o.user_id, o.total_amount, o.status, o.create_time, o.pay_time, " +
            "COUNT(oi.id) as item_count, " +
            "u.username as user_name " +
            "FROM orders o " +
            "LEFT JOIN order_item oi ON o.id = oi.order_id " +
            "LEFT JOIN user u ON o.user_id = u.id " +
            "WHERE (o.order_no LIKE CONCAT('%', #{keyword}, '%') " +
            "OR u.username LIKE CONCAT('%', #{keyword}, '%')) " +
            "GROUP BY o.id " +
            "ORDER BY o.create_time DESC")
    List<Map<String, Object>> searchOrdersForAdmin(String keyword);

    /**
     * 按状态搜索订单列表（管理员）
     */
    @Select("SELECT o.id, o.order_no, o.user_id, o.total_amount, o.status, o.create_time, o.pay_time, " +
            "COUNT(oi.id) as item_count, " +
            "u.username as user_name " +
            "FROM orders o " +
            "LEFT JOIN order_item oi ON o.id = oi.order_id " +
            "LEFT JOIN user u ON o.user_id = u.id " +
            "WHERE o.status = #{status} " +
            "AND (o.order_no LIKE CONCAT('%', #{keyword}, '%') " +
            "OR u.username LIKE CONCAT('%', #{keyword}, '%')) " +
            "GROUP BY o.id " +
            "ORDER BY o.create_time DESC")
    List<Map<String, Object>> searchOrdersByStatusForAdmin(Integer status, String keyword);
}
