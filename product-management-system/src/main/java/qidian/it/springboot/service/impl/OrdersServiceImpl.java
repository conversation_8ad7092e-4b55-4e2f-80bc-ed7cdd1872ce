package qidian.it.springboot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import qidian.it.springboot.entity.*;
import qidian.it.springboot.mapper.OrderItemMapper;
import qidian.it.springboot.mapper.OrdersMapper;
import qidian.it.springboot.service.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 订单服务实现类
 */
@Service
public class OrdersServiceImpl extends ServiceImpl<OrdersMapper, Orders> implements OrdersService {
    
    @Autowired
    private OrdersMapper ordersMapper;
    
    @Autowired
    private OrderItemMapper orderItemMapper;
    
    @Autowired
    private CartService cartService;
    
    @Autowired
    private ProductService productService;

    @Autowired
    private StockService stockService;
    
    @Override
    @Transactional
    public Orders createOrderFromCart(Long userId, List<Long> cartItemIds) {
        // 获取购物车商品信息
        List<Map<String, Object>> cartItems = cartService.getCartWithProductByUserId(userId);

        if (cartItems.isEmpty()) {
            throw new RuntimeException("购物车为空");
        }

        // 计算订单总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<OrderItem> orderItems = new ArrayList<>();

        for (Map<String, Object> cartItem : cartItems) {
            Long cartItemId = Long.valueOf(cartItem.get("id").toString());

            if (cartItemIds == null || cartItemIds.contains(cartItemId)) {
                Long productId = Long.valueOf(cartItem.get("product_id").toString());
                Integer quantity = Integer.valueOf(cartItem.get("quantity").toString());
                BigDecimal price = new BigDecimal(cartItem.get("price").toString());
                BigDecimal subtotal = price.multiply(new BigDecimal(quantity));

                totalAmount = totalAmount.add(subtotal);
                orderItems.add(new OrderItem(null, productId, quantity, price));
            }
        }

        if (orderItems.isEmpty()) {
            throw new RuntimeException("没有选择商品");
        }

        // 检查库存是否足够
        List<Map<String, Object>> stockCheckItems = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            Map<String, Object> item = new HashMap<>();
            item.put("productId", orderItem.getProductId());
            item.put("quantity", orderItem.getQuantity());
            stockCheckItems.add(item);
        }

        if (!stockService.batchCheckStockAvailable(stockCheckItems)) {
            throw new RuntimeException("部分商品库存不足，请重新选择");
        }

        // 创建订单
        String orderNo = generateOrderNo();
        Orders order = new Orders(userId, orderNo, totalAmount);
        order.setCreateTime(LocalDateTime.now());

        // 保存订单
        save(order);
        
        // 保存订单项
        for (OrderItem orderItem : orderItems) {
            orderItem.setOrderId(order.getId());
            orderItem.setSubtotal(orderItem.getPrice().multiply(new BigDecimal(orderItem.getQuantity())));
            orderItemMapper.insert(orderItem);
        }
        
        // 清空购物车中的相关商品
        if (cartItemIds != null) {
            for (Long cartItemId : cartItemIds) {
                // 这里需要根据购物车项ID删除，需要修改CartService
                // cartService.removeCartItem(cartItemId);
            }
        }
        
        return order;
    }
    
    @Override
    @Transactional
    public Orders createOrderDirect(Long userId, Long productId, Integer quantity) {
        // 获取商品信息
        Product product = productService.getById(productId);
        if (product == null) {
            throw new RuntimeException("商品不存在");
        }

        // 检查库存是否足够
        if (!stockService.checkStockAvailable(productId, quantity)) {
            throw new RuntimeException("库存不足，当前库存无法满足购买数量");
        }

        // 计算订单金额
        BigDecimal totalAmount = product.getPrice().multiply(new BigDecimal(quantity));

        // 创建订单
        String orderNo = generateOrderNo();
        Orders order = new Orders(userId, orderNo, totalAmount);
        order.setCreateTime(LocalDateTime.now());

        // 保存订单
        save(order);

        // 保存订单项
        OrderItem orderItem = new OrderItem(order.getId(), productId, quantity, product.getPrice());
        orderItemMapper.insert(orderItem);

        return order;
    }
    
    @Override
    public List<Map<String, Object>> getUserOrders(Long userId) {
        return ordersMapper.selectOrdersByUserId(userId);
    }
    
    @Override
    public Map<String, Object> getOrderDetail(Long orderId) {
        List<Map<String, Object>> orderDetails = ordersMapper.selectOrderDetailById(orderId);
        
        if (orderDetails.isEmpty()) {
            return null;
        }
        
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> firstRow = orderDetails.get(0);
        
        // 订单基本信息
        result.put("orderId", firstRow.get("order_id"));
        result.put("orderNo", firstRow.get("order_no"));
        result.put("totalAmount", firstRow.get("total_amount"));
        result.put("status", firstRow.get("status"));
        result.put("createTime", firstRow.get("create_time"));
        result.put("payTime", firstRow.get("pay_time"));
        
        // 订单项列表
        List<Map<String, Object>> items = new ArrayList<>();
        for (Map<String, Object> row : orderDetails) {
            if (row.get("item_id") != null) {
                Map<String, Object> item = new HashMap<>();
                item.put("itemId", row.get("item_id"));
                item.put("productId", row.get("product_id"));
                item.put("productName", row.get("product_name"));
                item.put("description", row.get("description"));
                item.put("imageUrl", row.get("image_url"));
                item.put("quantity", row.get("quantity"));
                item.put("price", row.get("price"));
                item.put("subtotal", row.get("subtotal"));
                items.add(item);
            }
        }
        result.put("items", items);
        
        return result;
    }
    
    @Override
    public Orders getOrderByOrderNo(String orderNo) {
        return ordersMapper.selectByOrderNo(orderNo);
    }
    
    @Override
    @Transactional
    public boolean updatePayStatus(String orderNo, Integer status) {
        LocalDateTime payTime = (status == 1) ? LocalDateTime.now() : null;
        int result = ordersMapper.updatePayStatus(orderNo, status, payTime);

        // 如果是支付成功，扣减库存
        if (result > 0 && status == 1) {
            try {
                reduceStockAfterPayment(orderNo);
            } catch (Exception e) {
                System.err.println("支付成功后扣减库存失败：" + e.getMessage());
                // 这里可以选择回滚支付状态或者记录日志进行后续处理
                throw new RuntimeException("支付成功但库存扣减失败：" + e.getMessage());
            }
        }

        return result > 0;
    }

    /**
     * 支付成功后扣减库存
     */
    @Transactional
    public void reduceStockAfterPayment(String orderNo) {
        // 获取订单信息
        Orders order = getOrderByOrderNo(orderNo);
        if (order == null) {
            throw new RuntimeException("订单不存在：" + orderNo);
        }

        // 获取订单详情
        Map<String, Object> orderDetail = getOrderDetail(order.getId());
        if (orderDetail == null) {
            throw new RuntimeException("订单详情不存在：" + orderNo);
        }

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> items = (List<Map<String, Object>>) orderDetail.get("items");

        // 准备库存扣减数据
        List<Map<String, Object>> stockItems = new ArrayList<>();
        for (Map<String, Object> item : items) {
            Map<String, Object> stockItem = new HashMap<>();
            stockItem.put("productId", item.get("productId"));
            stockItem.put("quantity", item.get("quantity"));
            stockItems.add(stockItem);
        }

        // 批量扣减库存
        if (!stockService.batchReduceStock(stockItems)) {
            throw new RuntimeException("批量扣减库存失败");
        }

        System.out.println("订单 " + orderNo + " 支付成功，库存扣减完成");
    }
    
    @Override
    @Transactional
    public boolean cancelOrder(Long orderId, Long userId) {
        Orders order = getById(orderId);
        if (order == null || !order.getUserId().equals(userId)) {
            return false;
        }

        if (order.getStatus() != 0 && order.getStatus() != 1) {
            throw new RuntimeException("只能取消待支付或已支付订单");
        }

        // 如果是已支付订单，需要回滚库存
        if (order.getStatus() == 1) {
            try {
                restoreStockAfterCancel(order.getId());
            } catch (Exception e) {
                System.err.println("取消订单回滚库存失败：" + e.getMessage());
                throw new RuntimeException("取消订单失败：" + e.getMessage());
            }
        }

        order.setStatus(4); // 已取消
        return updateById(order);
    }

    /**
     * 取消订单后回滚库存
     */
    @Transactional
    public void restoreStockAfterCancel(Long orderId) {
        // 获取订单详情
        Map<String, Object> orderDetail = getOrderDetail(orderId);
        if (orderDetail == null) {
            throw new RuntimeException("订单详情不存在：" + orderId);
        }

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> items = (List<Map<String, Object>>) orderDetail.get("items");

        // 回滚库存
        for (Map<String, Object> item : items) {
            Long productId = Long.valueOf(item.get("productId").toString());
            Integer quantity = Integer.valueOf(item.get("quantity").toString());

            if (!stockService.increaseStock(productId, quantity)) {
                throw new RuntimeException("回滚库存失败，商品ID：" + productId);
            }
        }

        System.out.println("订单 " + orderId + " 取消成功，库存回滚完成");
    }
    
    @Override
    @Transactional
    public boolean confirmOrder(Long orderId, Long userId) {
        Orders order = getById(orderId);
        if (order == null || !order.getUserId().equals(userId)) {
            return false;
        }
        
        if (order.getStatus() != 2) {
            throw new RuntimeException("只能确认已发货订单");
        }
        
        order.setStatus(3); // 已完成
        return updateById(order);
    }
    
    @Override
    public Map<String, Object> getOrderStatistics(Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        // 总订单数
        Integer totalCount = ordersMapper.countByUserId(userId);
        result.put("totalCount", totalCount);
        
        // 各状态订单数
        List<Map<String, Object>> statusCounts = ordersMapper.countOrdersByStatus(userId);
        Map<String, Integer> statusMap = new HashMap<>();
        statusMap.put("pending", 0);    // 待支付
        statusMap.put("paid", 0);       // 已支付
        statusMap.put("shipped", 0);    // 已发货
        statusMap.put("completed", 0);  // 已完成
        statusMap.put("cancelled", 0);  // 已取消
        
        for (Map<String, Object> statusCount : statusCounts) {
            Integer status = (Integer) statusCount.get("status");
            Integer count = ((Number) statusCount.get("count")).intValue();
            
            switch (status) {
                case 0: statusMap.put("pending", count); break;
                case 1: statusMap.put("paid", count); break;
                case 2: statusMap.put("shipped", count); break;
                case 3: statusMap.put("completed", count); break;
                case 4: statusMap.put("cancelled", count); break;
            }
        }
        
        result.put("statusCounts", statusMap);
        return result;
    }
    
    @Override
    public String generateOrderNo() {
        // 生成订单号：时间戳 + 随机数
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.valueOf((int) (Math.random() * 9000) + 1000);
        return timestamp + random;
    }

    @Override
    public List<Map<String, Object>> getUserPendingOrders(Long userId) {
        // 查询用户所有待支付订单（status = 0）
        return ordersMapper.selectPendingOrdersByUserId(userId);
    }

    // ========================================
    // 管理员订单管理方法实现
    // ========================================

    @Override
    public List<Map<String, Object>> getAllOrdersForAdmin() {
        return ordersMapper.selectAllOrdersForAdmin();
    }

    @Override
    @Transactional
    public boolean updateOrderStatusByAdmin(Long orderId, Integer status, String remark) {
        // 验证订单是否存在
        Orders order = getById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        // 验证状态转换是否合法
        if (!isValidStatusTransition(order.getStatus(), status)) {
            throw new RuntimeException("订单状态转换不合法");
        }

        // 更新订单状态
        int result = ordersMapper.updateOrderStatusByAdmin(orderId, status);

        // 如果是发货操作，可以在这里添加发货时间等逻辑
        if (status == 2 && result > 0) {
            // 发货操作
            System.out.println("订单 " + order.getOrderNo() + " 已发货，备注：" + remark);
        }

        return result > 0;
    }

    @Override
    public Map<String, Object> getAdminOrderStatistics() {
        return ordersMapper.selectAdminOrderStatistics();
    }

    @Override
    public List<Map<String, Object>> searchOrdersForAdmin(String keyword, Integer status) {
        // 如果关键词为空或只包含空格，返回所有订单或按状态筛选的订单
        if (keyword == null || keyword.trim().isEmpty()) {
            if (status != null) {
                // 按状态筛选
                return ordersMapper.selectAllOrdersForAdmin().stream()
                        .filter(order -> order.get("status").equals(status))
                        .collect(java.util.stream.Collectors.toList());
            } else {
                // 返回所有订单
                return ordersMapper.selectAllOrdersForAdmin();
            }
        }

        // 清理关键词
        keyword = keyword.trim();

        // 根据是否有状态筛选选择不同的查询方法
        if (status != null) {
            return ordersMapper.searchOrdersByStatusForAdmin(status, keyword);
        } else {
            return ordersMapper.searchOrdersForAdmin(keyword);
        }
    }

    /**
     * 验证订单状态转换是否合法
     */
    private boolean isValidStatusTransition(Integer currentStatus, Integer newStatus) {
        // 订单状态：0:待支付 1:已支付 2:已发货 3:已完成 4:已取消

        switch (currentStatus) {
            case 0: // 待支付
                return newStatus == 1 || newStatus == 4; // 可以支付或取消
            case 1: // 已支付
                return newStatus == 2 || newStatus == 4; // 可以发货或取消
            case 2: // 已发货
                return newStatus == 3; // 只能完成
            case 3: // 已完成
                return false; // 已完成不能再改变状态
            case 4: // 已取消
                return false; // 已取消不能再改变状态
            default:
                return false;
        }
    }
}
